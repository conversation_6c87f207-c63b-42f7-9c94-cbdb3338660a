import { Component, Host, h, Prop, State } from '@stencil/core';
import { isValid<PERSON>ey } from '../../utils/utils';

const SURVEY_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/public-surveys/sensePoll';
const SURVEY_API_ENDPOINT_DEV = 'http://localhost:4455/v1/public-surveys/sensePoll';
const SURVEY_API_ENDPOINT = window.location.hostname === 'localhost' ? SURVEY_API_ENDPOINT_DEV : SURVEY_API_ENDPOINT_PROD;

const RESPONSE_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/responses';
const RESPONSE_API_ENDPOINT_DEV = 'http://localhost:4466/v1/responses';
const RESPONSE_API_ENDPOINT = window.location.hostname === 'localhost' ? RESPONSE_API_ENDPOINT_DEV : RESPONSE_API_ENDPOINT_PROD;

enum SurveyStep {
  POLL = 0,
  FOLLOW_UP = 1,
  RESPONDENT_DETAILS = 2,
  THANK_YOU = 3,
}

interface Choice {
  label: string;
  value: string;
}

interface RespondentDetail {
  label: string;
  value: string;
  required?: boolean;
}

interface SurveyConfig {
  question: string;
  choiceType: string; // 'single' or 'multiple'
  choices: string[];
  followUpChoices?: string[];
  followUpQuestion?: string;
  thankYouMessage: string;
}

interface SurveyPayload {
  config: SurveyConfig;
  respondentDetails?: RespondentDetail[];
}

interface ApiResponse {
  success: boolean;
  message: string;
  payload: SurveyPayload;
}

@Component({
  tag: 'sf-sensepoll',
  styleUrl: 'sf-sensepoll.css',
  shadow: true,
})
export class SfSensepoll {
  @Prop() surveyKey: string;

  @State() config: SurveyConfig | null = null;

  @State() respondentDetails: RespondentDetail[] = [];

  @State() loading: boolean = false;

  @State() error: string | null = null;

  @State() currentStep: SurveyStep = SurveyStep.POLL;

  @State() selectedChoices: string[] = [];

  @State() selectedFollowUpChoices: string[] = [];

  @State() userRespondentDetails: { [key: string]: string } = {};

  @State() submitted: boolean = false;

  private surveyStartTime: number = 0;

  componentWillLoad() {
    if (isValidKey(this.surveyKey)) {
      return this.fetchSurveyData();
    }
    return Promise.resolve();
  }

  private async fetchSurveyData() {
    this.loading = true;
    this.error = null;
    this.surveyStartTime = Date.now();

    try {
      const response = await fetch(`${SURVEY_API_ENDPOINT}/${this.surveyKey}`);
      const data: ApiResponse = await response.json();

      if (!data.success) {
        throw new Error(data.message);
      }

      this.config = data.payload.config;
      this.respondentDetails = data.payload.respondentDetails || [];
    } catch (error) {
      this.error = error instanceof Error ? error.message : String(error);
    } finally {
      this.loading = false;
    }
  }

  private async retryOperation() {
    if (this.config) {
      // If we have config, retry submission
      await this.submitResponse();
    } else {
      // Otherwise, retry fetching survey data
      await this.fetchSurveyData();
    }
  }

  private async submitResponse() {
    if (!this.config || this.submitted) {
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      const completionTimeSeconds = this.surveyStartTime > 0 ? Math.round((Date.now() - this.surveyStartTime) / 1000) : 0;
      const userAgentInfo = this.getUserAgentInfo();

      const submissionData = {
        surveyPublicKey: this.surveyKey,
        responseData: {
          selectedChoices: this.selectedChoices,
          selectedFollowUpChoices: this.selectedFollowUpChoices,
        },
        respondentDetails: this.userRespondentDetails,
        userAgent: userAgentInfo,
        completionTime: completionTimeSeconds,
        surveyType: 'sensePoll',
      };

      const response = await fetch(`${RESPONSE_API_ENDPOINT}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message);
      }

      this.submitted = true;
      this.currentStep = SurveyStep.THANK_YOU;
    } catch (error) {
      this.error = error instanceof Error ? error.message : String(error);
    } finally {
      this.loading = false;
    }
  }

  private getUserAgentInfo() {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screenResolution: `${screen.width}x${screen.height}`,
      colorDepth: screen.colorDepth,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timestamp: new Date().toISOString(),
    };
  }

  private nextStep() {
    if (this.currentStep === SurveyStep.POLL) {
      if (this.hasFollowUpStep()) {
        this.currentStep = SurveyStep.FOLLOW_UP;
      } else if (this.hasRespondentDetailsStep()) {
        this.currentStep = SurveyStep.RESPONDENT_DETAILS;
      } else {
        this.submitResponse();
      }
    } else if (this.currentStep === SurveyStep.FOLLOW_UP) {
      if (this.hasRespondentDetailsStep()) {
        this.currentStep = SurveyStep.RESPONDENT_DETAILS;
      } else {
        this.submitResponse();
      }
    } else if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.submitResponse();
    }
  }

  private prevStep() {
    if (this.currentStep === SurveyStep.FOLLOW_UP) {
      this.currentStep = SurveyStep.POLL;
    } else if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      if (this.hasFollowUpStep()) {
        this.currentStep = SurveyStep.FOLLOW_UP;
      } else {
        this.currentStep = SurveyStep.POLL;
      }
    }
  }

  private hasFollowUpStep(): boolean {
    return !!(this.config?.followUpQuestion && this.config?.followUpChoices?.length);
  }

  private hasRespondentDetailsStep(): boolean {
    return this.respondentDetails.length > 0;
  }

  private handleChoiceSelect(choice: string) {
    if (this.config?.choiceType === 'multiple') {
      if (this.selectedChoices.includes(choice)) {
        this.selectedChoices = this.selectedChoices.filter(c => c !== choice);
      } else {
        this.selectedChoices = [...this.selectedChoices, choice];
      }
    } else {
      this.selectedChoices = [choice];
    }
  }

  private handleFollowUpChoiceSelect(choice: string) {
    if (this.selectedFollowUpChoices.includes(choice)) {
      this.selectedFollowUpChoices = this.selectedFollowUpChoices.filter(c => c !== choice);
    } else {
      this.selectedFollowUpChoices = [...this.selectedFollowUpChoices, choice];
    }
  }

  private handleRespondentDetailChange(key: string, value: string) {
    this.userRespondentDetails = {
      ...this.userRespondentDetails,
      [key]: value,
    };
  }

  private isRespondentDetailsValid(): boolean {
    const requiredFields = this.respondentDetails.filter(detail => detail.required);
    return requiredFields.every(field => {
      const value = this.userRespondentDetails[field.value];
      return value && value.trim().length > 0;
    });
  }

  private renderPollStep() {
    const isFinalStep = !this.hasFollowUpStep() && !this.hasRespondentDetailsStep();
    const isMultiple = this.config?.choiceType === 'multiple';

    return (
      <div part="step poll-step">
        <h2 part="heading poll-heading">{this.config?.question || 'What is your choice?'}</h2>
        <div part="choices-container">
          {this.config?.choices?.map(choice => (
            <label part="choice-option">
              <input
                part={isMultiple ? 'checkbox-input' : 'radio-input'}
                type={isMultiple ? 'checkbox' : 'radio'}
                name="poll-choice"
                value={choice}
                checked={this.selectedChoices.includes(choice)}
                onChange={() => this.handleChoiceSelect(choice)}
              />
              <span part="choice-label">{choice}</span>
            </label>
          ))}
        </div>
        <div part="button-container">
          <button part="button next-button" onClick={() => this.nextStep()} disabled={this.selectedChoices.length === 0}>
            {isFinalStep ? 'Submit' : 'Next'}
          </button>
        </div>
      </div>
    );
  }

  private renderFollowUpStep() {
    const isFinalStep = !this.hasRespondentDetailsStep();

    return (
      <div part="step follow-up-step">
        <h2 part="heading follow-up-heading">{this.config?.followUpQuestion || 'Follow-up question'}</h2>
        <div part="choices-container">
          {this.config?.followUpChoices?.map(choice => (
            <label part="choice-option">
              <input
                part="checkbox-input"
                type="checkbox"
                name="follow-up-choice"
                value={choice}
                checked={this.selectedFollowUpChoices.includes(choice)}
                onChange={() => this.handleFollowUpChoiceSelect(choice)}
              />
              <span part="choice-label">{choice}</span>
            </label>
          ))}
        </div>
        <div part="button-container">
          <button part="button back-button" onClick={() => this.prevStep()}>
            Back
          </button>
          <button part="button next-button" onClick={() => this.nextStep()}>
            {isFinalStep ? 'Submit' : 'Next'}
          </button>
        </div>
      </div>
    );
  }

  private renderRespondentDetailsStep() {
    return (
      <div part="step respondent-details-step">
        <h2 part="heading respondent-details-heading">Tell us about yourself</h2>
        <div part="form-container">
          {this.respondentDetails.map(detail => (
            <div part="form-field">
              <label part="form-label">
                {detail.label}
                {detail.required && <span part="required-indicator"> *</span>}
              </label>
              <input
                part="input form-input"
                type="text"
                value={this.userRespondentDetails[detail.value] || ''}
                onInput={e => this.handleRespondentDetailChange(detail.value, (e.target as HTMLInputElement).value)}
                required={detail.required}
              />
            </div>
          ))}
        </div>
        <div part="button-container">
          <button part="button back-button" onClick={() => this.prevStep()}>
            Back
          </button>
          <button part="button submit-button" onClick={() => this.submitResponse()} disabled={!this.isRespondentDetailsValid()}>
            Submit
          </button>
        </div>
      </div>
    );
  }

  private renderThankYouStep() {
    return (
      <div part="step thank-you-step">
        <h2 part="heading thank-you-heading">{this.config?.thankYouMessage || 'Thank you for your response!'}</h2>
      </div>
    );
  }

  private renderCurrentStep() {
    const stepRenderers = {
      [SurveyStep.POLL]: this.renderPollStep.bind(this),
      [SurveyStep.FOLLOW_UP]: this.renderFollowUpStep.bind(this),
      [SurveyStep.RESPONDENT_DETAILS]: this.renderRespondentDetailsStep.bind(this),
      [SurveyStep.THANK_YOU]: this.renderThankYouStep.bind(this),
    };

    const renderer = stepRenderers[this.currentStep] || stepRenderers[SurveyStep.POLL];

    return renderer();
  }

  render() {
    if (!isValidKey(this.surveyKey)) {
      return (
        <Host>
          <p part="message error-message">Unable to render survey due to invalid public key</p>
        </Host>
      );
    }

    if (this.loading) {
      return (
        <Host>
          <p part="message loading-message">Loading survey...</p>
        </Host>
      );
    }

    if (this.error) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">{this.error}</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    if (!this.config) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">No survey configuration found</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    return (
      <Host>
        <div part="survey-container">{this.renderCurrentStep()}</div>
      </Host>
    );
  }
}
