import { Component, Host, h, Prop, State } from '@stencil/core';
import { isValidKey } from '../../utils/utils';

const SURVEY_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/public-surveys/senseChoice';
const SURVEY_API_ENDPOINT_DEV = 'http://localhost:4455/v1/public-surveys/senseChoice';
const SURVEY_API_ENDPOINT = window.location.hostname === 'localhost' ? SURVEY_API_ENDPOINT_DEV : SURVEY_API_ENDPOINT_PROD;

const RESPONSE_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/responses';
const RESPONSE_API_ENDPOINT_DEV = 'http://localhost:4466/v1/responses';
const RESPONSE_API_ENDPOINT = window.location.hostname === 'localhost' ? RESPONSE_API_ENDPOINT_DEV : RESPONSE_API_ENDPOINT_PROD;

enum SurveyStep {
  CHOICE_TASK = 0,
  RESPONDENT_DETAILS = 1,
  THANK_YOU = 2,
}

interface ConjointConcept {
  conceptId: string;
  attributes: { [key: string]: string }; // attributeValue -> selected variant value
  selected?: boolean;
}

interface ConjointChoiceTask {
  taskId: string;
  taskNumber: number;
  alternatives: ConjointConcept[];
  includeNoneOption: boolean;
  selected?: boolean;
}

interface RespondentDetail {
  label: string;
  value: string;
  required?: boolean;
}

interface SurveyConfig {
  type: string; // 'lite' or 'full'
  attributes: { label: string; value: string }[];
  attributeVariants: { [key: string]: { label: string; value: string }[] }; // attributeValue -> variants array
  selectedConcepts: ConjointConcept[]; // User-curated concepts
  choiceTasks: ConjointChoiceTask[]; // User-curated tasks
  thankYouMessage: string;
}

interface SurveyPayload {
  config: SurveyConfig;
  respondentDetails?: RespondentDetail[];
}

interface ApiResponse {
  success: boolean;
  message: string;
  payload: SurveyPayload;
}

interface TaskResponse {
  taskId: string;
  selectedConceptId: string | null; // null for "none of these"
}

@Component({
  tag: 'sf-sensechoice',
  styleUrl: 'sf-sensechoice.css',
  shadow: true,
})
export class SfSensechoice {
  @Prop() surveyKey: string;

  @State() config: SurveyConfig | null = null;

  @State() respondentDetails: RespondentDetail[] = [];

  @State() loading: boolean = false;

  @State() error: string | null = null;

  @State() currentStep: SurveyStep = SurveyStep.CHOICE_TASK;

  @State() currentTaskIndex: number = 0;

  @State() taskResponses: TaskResponse[] = [];

  @State() userRespondentDetails: { [key: string]: string } = {};

  @State() submitted: boolean = false;

  private surveyStartTime: number = 0;

  componentWillLoad() {
    if (isValidKey(this.surveyKey)) {
      return this.fetchSurveyData();
    }
    return Promise.resolve();
  }

  private async fetchSurveyData() {
    this.loading = true;
    this.error = null;
    this.surveyStartTime = Date.now();

    try {
      const response = await fetch(`${SURVEY_API_ENDPOINT}/${this.surveyKey}`);
      const data: ApiResponse = await response.json();

      if (!data.success) {
        throw new Error(data.message);
      }

      this.config = data.payload.config;
      this.respondentDetails = data.payload.respondentDetails || [];

      // Initialize task responses
      if (this.config?.choiceTasks) {
        this.taskResponses = this.config.choiceTasks.map(task => ({
          taskId: task.taskId,
          selectedConceptId: null,
        }));
      }
    } catch (error) {
      this.error = error instanceof Error ? error.message : String(error);
    } finally {
      this.loading = false;
    }
  }

  private async retryOperation() {
    if (this.config) {
      // If we have config, retry submission
      await this.submitResponse();
    } else {
      // Otherwise, retry fetching survey data
      await this.fetchSurveyData();
    }
  }

  private async submitResponse() {
    if (!this.config || this.submitted || this.taskResponses.length === 0) {
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      const completionTimeSeconds = this.surveyStartTime > 0 ? Math.round((Date.now() - this.surveyStartTime) / 1000) : 0;
      const userAgentInfo = this.getUserAgentInfo();

      const submissionData = {
        surveyPublicKey: this.surveyKey,
        responseData: {
          choiceResponses: this.taskResponses,
          surveyType: this.config.type,
        },
        respondentDetails: this.userRespondentDetails,
        userAgent: userAgentInfo,
        completionTime: completionTimeSeconds,
        surveyType: 'senseChoice',
      };

      const response = await fetch(`${RESPONSE_API_ENDPOINT}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message);
      }

      this.submitted = true;
      this.currentStep = SurveyStep.THANK_YOU;
    } catch (error) {
      this.error = error instanceof Error ? error.message : String(error);
    } finally {
      this.loading = false;
    }
  }

  private getUserAgentInfo() {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screenResolution: `${screen.width}x${screen.height}`,
      colorDepth: screen.colorDepth,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timestamp: new Date().toISOString(),
    };
  }

  private nextStep() {
    if (this.currentStep === SurveyStep.CHOICE_TASK) {
      if (this.hasRespondentDetailsStep()) {
        this.currentStep = SurveyStep.RESPONDENT_DETAILS;
      } else {
        this.submitResponse();
      }
    } else if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.submitResponse();
    }
  }

  private prevStep() {
    if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.currentStep = SurveyStep.CHOICE_TASK;
    }
  }

  private hasRespondentDetailsStep(): boolean {
    return this.respondentDetails.length > 0;
  }

  private handleRespondentDetailChange(key: string, value: string) {
    this.userRespondentDetails = {
      ...this.userRespondentDetails,
      [key]: value,
    };
  }

  private isRespondentDetailsValid(): boolean {
    const requiredFields = this.respondentDetails.filter(detail => detail.required);
    return requiredFields.every(field => {
      const value = this.userRespondentDetails[field.value];
      return value && value.trim().length > 0;
    });
  }

  private getCurrentTask(): ConjointChoiceTask | null {
    if (!this.config?.choiceTasks || this.currentTaskIndex >= this.config.choiceTasks.length) {
      return null;
    }
    return this.config.choiceTasks[this.currentTaskIndex];
  }

  private getCurrentTaskResponse(): TaskResponse | null {
    const currentTask = this.getCurrentTask();
    if (!currentTask) return null;

    return this.taskResponses.find(response => response.taskId === currentTask.taskId) || null;
  }

  private handleConceptSelection(conceptId: string | null) {
    const currentTask = this.getCurrentTask();
    if (!currentTask) return;

    const responseIndex = this.taskResponses.findIndex(response => response.taskId === currentTask.taskId);
    if (responseIndex >= 0) {
      this.taskResponses = [
        ...this.taskResponses.slice(0, responseIndex),
        { ...this.taskResponses[responseIndex], selectedConceptId: conceptId },
        ...this.taskResponses.slice(responseIndex + 1),
      ];
    }
  }

  private nextTask() {
    if (this.currentTaskIndex < (this.config?.choiceTasks?.length || 0) - 1) {
      this.currentTaskIndex++;
    } else {
      // All tasks completed, move to next step
      this.nextStep();
    }
  }

  private prevTask() {
    if (this.currentTaskIndex > 0) {
      this.currentTaskIndex--;
    }
  }

  private isCurrentTaskCompleted(): boolean {
    const response = this.getCurrentTaskResponse();
    return response !== null && response.selectedConceptId !== undefined;
  }

  private getAttributeLabel(attributeValue: string): string {
    const attribute = this.config?.attributes?.find(attr => attr.value === attributeValue);
    return attribute?.label || attributeValue;
  }

  private getVariantLabel(attributeValue: string, variantValue: string): string {
    const variants = this.config?.attributeVariants?.[attributeValue];
    const variant = variants?.find(v => v.value === variantValue);
    return variant?.label || variantValue;
  }

  private renderConcept(concept: ConjointConcept, isSelected: boolean, onSelect: () => void) {
    return (
      <div part={`concept ${isSelected ? 'concept-selected' : 'concept-unselected'}`} onClick={onSelect}>
        <div part="concept-header">
          <input part="concept-radio" type="radio" checked={isSelected} onChange={onSelect} />
          <span part="concept-title">Option {concept.conceptId}</span>
        </div>
        <div part="concept-attributes">
          {Object.entries(concept.attributes).map(([attributeValue, variantValue]) => (
            <div part="concept-attribute">
              <span part="attribute-label">{this.getAttributeLabel(attributeValue)}:</span>
              <span part="attribute-value">{this.getVariantLabel(attributeValue, variantValue)}</span>
            </div>
          ))}
        </div>
      </div>
    );
  }

  private renderChoiceTaskStep() {
    const currentTask = this.getCurrentTask();
    const currentResponse = this.getCurrentTaskResponse();

    if (!currentTask) {
      return (
        <div part="step choice-task-step">
          <p part="message error-message">No choice tasks available</p>
        </div>
      );
    }

    const totalTasks = this.config?.choiceTasks?.length || 0;
    const isLastTask = this.currentTaskIndex === totalTasks - 1;
    const isFinalStep = isLastTask && !this.hasRespondentDetailsStep();

    return (
      <div part="step choice-task-step">
        <div part="task-header">
          <h2 part="heading task-heading">
            Choice Task {currentTask.taskNumber} of {totalTasks}
          </h2>
          <p part="task-instructions">Please select your preferred option from the alternatives below:</p>
        </div>

        <div part="concepts-container">
          {currentTask.alternatives.map(concept =>
            this.renderConcept(concept, currentResponse?.selectedConceptId === concept.conceptId, () => this.handleConceptSelection(concept.conceptId)),
          )}

          {currentTask.includeNoneOption && (
            <div
              part={`concept none-option ${currentResponse?.selectedConceptId === null ? 'concept-selected' : 'concept-unselected'}`}
              onClick={() => this.handleConceptSelection(null)}
            >
              <div part="concept-header">
                <input part="concept-radio" type="radio" checked={currentResponse?.selectedConceptId === null} onChange={() => this.handleConceptSelection(null)} />
                <span part="concept-title">None of these options</span>
              </div>
            </div>
          )}
        </div>

        <div part="button-container">
          {this.currentTaskIndex > 0 && (
            <button part="button back-button" onClick={() => this.prevTask()}>
              Previous
            </button>
          )}
          <button part="button next-button" onClick={() => this.nextTask()} disabled={!this.isCurrentTaskCompleted()}>
            {isLastTask ? (isFinalStep ? 'Submit' : 'Next') : 'Next Task'}
          </button>
        </div>

        <div part="progress-indicator">
          <div part="progress-bar">
            <div part="progress-fill" style={{ width: `${((this.currentTaskIndex + 1) / totalTasks) * 100}%` }}></div>
          </div>
          <span part="progress-text">
            Task {this.currentTaskIndex + 1} of {totalTasks}
          </span>
        </div>
      </div>
    );
  }

  private renderRespondentDetailsStep() {
    return (
      <div part="step respondent-details-step">
        <h2 part="heading respondent-details-heading">Tell us about yourself</h2>
        <div part="form-container">
          {this.respondentDetails.map(detail => (
            <div part="form-field">
              <label part="form-label">
                {detail.label}
                {detail.required && <span part="required-indicator"> *</span>}
              </label>
              <input
                part="input form-input"
                type="text"
                value={this.userRespondentDetails[detail.value] || ''}
                onInput={e => this.handleRespondentDetailChange(detail.value, (e.target as HTMLInputElement).value)}
                required={detail.required}
              />
            </div>
          ))}
        </div>
        <div part="button-container">
          <button part="button back-button" onClick={() => this.prevStep()}>
            Back
          </button>
          <button part="button submit-button" onClick={() => this.submitResponse()} disabled={!this.isRespondentDetailsValid()}>
            Submit
          </button>
        </div>
      </div>
    );
  }

  private renderThankYouStep() {
    return (
      <div part="step thank-you-step">
        <h2 part="heading thank-you-heading">{this.config?.thankYouMessage || 'Thank you for your response!'}</h2>
        <div part="completion-summary">
          <p part="summary-text">You completed {this.config?.choiceTasks?.length || 0} choice tasks.</p>
        </div>
      </div>
    );
  }

  private renderCurrentStep() {
    const stepRenderers = {
      [SurveyStep.CHOICE_TASK]: this.renderChoiceTaskStep.bind(this),
      [SurveyStep.RESPONDENT_DETAILS]: this.renderRespondentDetailsStep.bind(this),
      [SurveyStep.THANK_YOU]: this.renderThankYouStep.bind(this),
    };

    const renderer = stepRenderers[this.currentStep] || stepRenderers[SurveyStep.CHOICE_TASK];

    return renderer();
  }

  render() {
    if (!isValidKey(this.surveyKey)) {
      return (
        <Host>
          <p part="message error-message">Unable to render survey due to invalid public key</p>
        </Host>
      );
    }

    if (this.loading) {
      return (
        <Host>
          <p part="message loading-message">Loading survey...</p>
        </Host>
      );
    }

    if (this.error) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">{this.error}</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    if (!this.config) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">No survey configuration found</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    return (
      <Host>
        <div part="survey-container">{this.renderCurrentStep()}</div>
      </Host>
    );
  }
}
