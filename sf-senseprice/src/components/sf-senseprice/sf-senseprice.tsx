import { Component, Host, h, Prop, State } from '@stencil/core';
import { isValidKey, formatErrorMessage, formatCurrency, isValidPrice } from '../../utils/utils';

const SURVEY_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/public-surveys/sensePrice';
const SURVEY_API_ENDPOINT_DEV = 'http://localhost:4455/v1/public-surveys/sensePrice';
const SURVEY_API_ENDPOINT = window.location.hostname === 'localhost' ? SURVEY_API_ENDPOINT_DEV : SURVEY_API_ENDPOINT_PROD;

const RESPONSE_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/responses';
const RESPONSE_API_ENDPOINT_DEV = 'http://localhost:4466/v1/responses';
const RESPONSE_API_ENDPOINT = window.location.hostname === 'localhost' ? RESPONSE_API_ENDPOINT_DEV : RESPONSE_API_ENDPOINT_PROD;

enum SurveyStep {
  PRICE_INPUT = 0,
  RESPONDENT_DETAILS = 1,
  THANK_YOU = 2,
}

interface RespondentDetail {
  label: string;
  value: string;
  required?: boolean;
}

interface SurveyConfig {
  currency: string;
  priceType: string; // 'recurring' or 'non-recurring'
  recurringBasis?: string; // 'monthly' or 'annual' (only if priceType is 'recurring')
  thankYouMessage: string;
}

interface SurveyPayload {
  config: SurveyConfig;
  respondentDetails?: RespondentDetail[];
  respondent_details?: RespondentDetail[];
}

interface ApiResponse {
  success: boolean;
  message: string;
  payload: SurveyPayload;
}

@Component({
  tag: 'sf-senseprice',
  styleUrl: 'sf-senseprice.css',
  shadow: true,
})
export class SfSenseprice {
  @Prop() surveyKey: string;

  @State() config: SurveyConfig | null = null;

  @State() respondentDetails: RespondentDetail[] = [];

  @State() loading: boolean = false;

  @State() error: string | null = null;

  @State() currentStep: SurveyStep = SurveyStep.PRICE_INPUT;

  @State() priceInput: string = '';

  @State() userRespondentDetails: { [key: string]: string } = {};

  @State() submitted: boolean = false;

  private surveyStartTime: number = 0;

  componentWillLoad() {
    if (isValidKey(this.surveyKey)) {
      return this.fetchSurveyData();
    }
    return Promise.resolve();
  }

  private async fetchSurveyData() {
    this.loading = true;
    this.error = null;
    this.surveyStartTime = Date.now();

    try {
      const response = await fetch(`${SURVEY_API_ENDPOINT}/${this.surveyKey}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch survey: ${response.statusText}`);
      }

      const data: ApiResponse = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to load survey');
      }

      this.config = data.payload.config;
      // Handle both camelCase and snake_case field names from API
      this.respondentDetails = data.payload.respondentDetails || data.payload.respondent_details || [];
    } catch (error) {
      this.error = formatErrorMessage(error);
    } finally {
      this.loading = false;
    }
  }

  private async retryOperation() {
    if (this.config) {
      // If we have config, retry submission
      await this.submitResponse();
    } else {
      // Otherwise, retry fetching survey data
      await this.fetchSurveyData();
    }
  }

  private async submitResponse() {
    if (!this.config || this.submitted || !isValidPrice(this.priceInput)) {
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      const completionTimeSeconds = this.surveyStartTime > 0 ? Math.round((Date.now() - this.surveyStartTime) / 1000) : 0;
      const userAgentInfo = this.getUserAgentInfo();

      const submissionData = {
        surveyPublicKey: this.surveyKey,
        responseData: {
          price: parseFloat(this.priceInput),
          currency: this.config.currency,
          priceType: this.config.priceType,
          recurringBasis: this.config.recurringBasis,
        },
        respondentDetails: this.userRespondentDetails,
        userAgent: userAgentInfo,
        completionTime: completionTimeSeconds,
        surveyType: 'sensePrice',
      };

      const response = await fetch(`${RESPONSE_API_ENDPOINT}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      if (!response.ok) {
        throw new Error(`Failed to submit response: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Failed to submit response');
      }

      this.submitted = true;
      this.currentStep = SurveyStep.THANK_YOU;
    } catch (error) {
      this.error = formatErrorMessage(error);
    } finally {
      this.loading = false;
    }
  }

  private getUserAgentInfo() {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screenResolution: `${screen.width}x${screen.height}`,
      colorDepth: screen.colorDepth,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timestamp: new Date().toISOString(),
    };
  }

  private nextStep() {
    if (this.currentStep === SurveyStep.PRICE_INPUT) {
      if (this.hasRespondentDetailsStep()) {
        this.currentStep = SurveyStep.RESPONDENT_DETAILS;
      } else {
        this.submitResponse();
      }
    } else if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.submitResponse();
    }
  }

  private prevStep() {
    if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.currentStep = SurveyStep.PRICE_INPUT;
    }
  }

  private hasRespondentDetailsStep(): boolean {
    return this.respondentDetails.length > 0;
  }

  private handlePriceChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.priceInput = target.value;
  }

  private handleRespondentDetailChange(key: string, value: string) {
    this.userRespondentDetails = {
      ...this.userRespondentDetails,
      [key]: value,
    };
  }

  private isRespondentDetailsValid(): boolean {
    const requiredFields = this.respondentDetails.filter(detail => detail.required);
    return requiredFields.every(field => {
      const value = this.userRespondentDetails[field.value];
      return value && value.trim().length > 0;
    });
  }

  private isPriceValid(): boolean {
    return isValidPrice(this.priceInput) && this.priceInput.trim().length > 0;
  }

  private getFormattedPrice(): string {
    if (!this.isPriceValid() || !this.config) {
      return '';
    }
    return formatCurrency(parseFloat(this.priceInput), this.config.currency);
  }

  private getPriceLabel(): string {
    if (!this.config) {
      return 'Enter your price';
    }

    let label = `Enter your price in ${this.config.currency.toUpperCase()}`;

    if (this.config.priceType === 'recurring' && this.config.recurringBasis) {
      label += ` (${this.config.recurringBasis})`;
    }

    return label;
  }

  private renderPriceInputStep() {
    const isFinalStep = !this.hasRespondentDetailsStep();

    return (
      <div part="step price-input-step">
        <h2 part="heading price-heading">{this.getPriceLabel()}</h2>
        <div part="price-input-container">
          <div part="currency-symbol">{this.config?.currency?.toUpperCase() || '$'}</div>
          <input part="input price-input" type="number" min="0" step="0.01" value={this.priceInput} onInput={e => this.handlePriceChange(e)} placeholder="0.00" />
          {this.config?.priceType === 'recurring' && this.config?.recurringBasis && <div part="recurring-indicator">/{this.config.recurringBasis}</div>}
        </div>
        {this.isPriceValid() && (
          <div part="price-preview">
            Preview: {this.getFormattedPrice()}
            {this.config?.priceType === 'recurring' && this.config?.recurringBasis && ` per ${this.config.recurringBasis.slice(0, -2)}`}
          </div>
        )}
        <div part="button-container">
          <button part="button next-button" onClick={() => this.nextStep()} disabled={!this.isPriceValid()}>
            {isFinalStep ? 'Submit' : 'Next'}
          </button>
        </div>
      </div>
    );
  }

  private renderRespondentDetailsStep() {
    return (
      <div part="step respondent-details-step">
        <h2 part="heading respondent-details-heading">Tell us about yourself</h2>
        <div part="form-container">
          {this.respondentDetails.map(detail => (
            <div part="form-field">
              <label part="form-label">
                {detail.label}
                {detail.required && <span part="required-indicator"> *</span>}
              </label>
              <input
                part="input form-input"
                type="text"
                value={this.userRespondentDetails[detail.value] || ''}
                onInput={e => this.handleRespondentDetailChange(detail.value, (e.target as HTMLInputElement).value)}
                required={detail.required}
              />
            </div>
          ))}
        </div>
        <div part="button-container">
          <button part="button back-button" onClick={() => this.prevStep()}>
            Back
          </button>
          <button part="button submit-button" onClick={() => this.submitResponse()} disabled={!this.isRespondentDetailsValid()}>
            Submit
          </button>
        </div>
      </div>
    );
  }

  private renderThankYouStep() {
    return (
      <div part="step thank-you-step">
        <h2 part="heading thank-you-heading">{this.config?.thankYouMessage || 'Thank you for your response!'}</h2>
        {this.isPriceValid() && this.config && (
          <div part="response-summary">
            <p part="summary-text">
              You indicated a price of {this.getFormattedPrice()}
              {this.config.priceType === 'recurring' && this.config.recurringBasis && ` per ${this.config.recurringBasis.slice(0, -2)}`}
            </p>
          </div>
        )}
      </div>
    );
  }

  private renderCurrentStep() {
    const stepRenderers = {
      [SurveyStep.PRICE_INPUT]: this.renderPriceInputStep.bind(this),
      [SurveyStep.RESPONDENT_DETAILS]: this.renderRespondentDetailsStep.bind(this),
      [SurveyStep.THANK_YOU]: this.renderThankYouStep.bind(this),
    };

    const renderer = stepRenderers[this.currentStep] || stepRenderers[SurveyStep.PRICE_INPUT];

    return renderer();
  }

  render() {
    if (!isValidKey(this.surveyKey)) {
      return (
        <Host>
          <p part="message error-message">Please provide a valid survey key</p>
        </Host>
      );
    }

    if (this.loading) {
      return (
        <Host>
          <p part="message loading-message">Loading survey...</p>
        </Host>
      );
    }

    if (this.error) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">{this.error}</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    if (!this.config) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">No survey configuration found</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    return (
      <Host>
        <div part="survey-container">{this.renderCurrentStep()}</div>
      </Host>
    );
  }
}
