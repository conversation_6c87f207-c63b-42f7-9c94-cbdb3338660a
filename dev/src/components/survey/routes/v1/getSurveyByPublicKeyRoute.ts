import { Router } from 'express';
import { ExtractOriginFromRequest, ExtractIPAddressFromOrigin, ExtractCountryFromIPAddress } from '../../../../global/middlewares';
import {
  validateGetSurveyByPublicKeyPayload,
  BlockRequestByDistribution,
  BlockRequestBySurveyTypeMismatch,
  getSurveyByPublicKeyFromCache,
  storeSurveyByPublicKeyInCache,
} from '../../middlewares';
import { getSurveyByPublicKeyController } from '../../controllers';
import { GenerateApiVersionPath } from '../../../../global/helpers';

export const getSurveyByPublicKeyRoute = Router();

getSurveyByPublicKeyRoute.get(
  `${GenerateApiVersionPath()}public-surveys/:publicKey/:surveyType`,
  ExtractOriginFromRequest,
  ExtractIPAddressFromOrigin,
  ExtractCountryFromIPAddress,
  validateGetSurveyByPublicKeyPayload,
  getSurveyByPublicKeyFromCache,
  BlockRequestByDistribution,
  BlockRequestBySurveyTypeMismatch,
  storeSurveyByPublicKeyInCache,
  getSurveyByPublicKeyController,
);
