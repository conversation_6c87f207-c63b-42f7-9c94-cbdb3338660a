import { Component, Host, h, Prop, State } from '@stencil/core';
import { isValidKey, formatErrorMessage, shuffleArray, moveArrayItem } from '../../utils/utils';

const SURVEY_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/public-surveys/sensePriority';
const SURVEY_API_ENDPOINT_DEV = 'http://localhost:4455/v1/public-surveys/sensePriority';
const SURVEY_API_ENDPOINT = window.location.hostname === 'localhost' ? SURVEY_API_ENDPOINT_DEV : SURVEY_API_ENDPOINT_PROD;

const RESPONSE_API_ENDPOINT_PROD = 'https://api.sensefolks.com/v1/responses';
const RESPONSE_API_ENDPOINT_DEV = 'http://localhost:4466/v1/responses';
const RESPONSE_API_ENDPOINT = window.location.hostname === 'localhost' ? RESPONSE_API_ENDPOINT_DEV : RESPONSE_API_ENDPOINT_PROD;

enum SurveyStep {
  PRIORITY_RANKING = 0,
  RESPONDENT_DETAILS = 1,
  THANK_YOU = 2,
}

interface PriorityItem {
  title: string;
  description?: string;
  value: string;
}

interface RespondentDetail {
  label: string;
  value: string;
  required?: boolean;
}

interface SurveyConfig {
  question: string;
  items: PriorityItem[];
  thankYouMessage: string;
}

interface SurveyPayload {
  config: SurveyConfig;
  respondentDetails?: RespondentDetail[];
  respondent_details?: RespondentDetail[];
}

interface ApiResponse {
  success: boolean;
  message: string;
  payload: SurveyPayload;
}

interface RankedItem extends PriorityItem {
  rank: number;
}

@Component({
  tag: 'sf-sensepriority',
  styleUrl: 'sf-sensepriority.css',
  shadow: true,
})
export class SfSensepriority {
  @Prop() surveyKey: string;

  @State() config: SurveyConfig | null = null;

  @State() respondentDetails: RespondentDetail[] = [];

  @State() loading: boolean = false;

  @State() error: string | null = null;

  @State() currentStep: SurveyStep = SurveyStep.PRIORITY_RANKING;

  @State() rankedItems: RankedItem[] = [];

  @State() userRespondentDetails: { [key: string]: string } = {};

  @State() submitted: boolean = false;

  @State() draggedIndex: number | null = null;

  private surveyStartTime: number = 0;

  componentWillLoad() {
    if (isValidKey(this.surveyKey)) {
      return this.fetchSurveyData();
    }
    return Promise.resolve();
  }

  private async fetchSurveyData() {
    this.loading = true;
    this.error = null;
    this.surveyStartTime = Date.now();

    try {
      const response = await fetch(`${SURVEY_API_ENDPOINT}/${this.surveyKey}`);

      if (!response.ok) {
        // Try to parse error response to get the actual error message
        try {
          const errorData = await response.json();
          throw new Error(errorData.message || `Failed to fetch survey: ${response.statusText}`);
        } catch (parseError) {
          // If parsing fails, fall back to status text
          throw new Error(`Failed to fetch survey: ${response.statusText}`);
        }
      }

      const data: ApiResponse = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to load survey');
      }

      this.config = data.payload.config;
      // Handle both camelCase and snake_case field names from API
      this.respondentDetails = data.payload.respondentDetails || data.payload.respondent_details || [];

      // Initialize ranked items with shuffled order
      if (this.config?.items) {
        const shuffledItems = shuffleArray(this.config.items);
        this.rankedItems = shuffledItems.map((item, index) => ({
          ...item,
          rank: index + 1,
        }));
      }
    } catch (error) {
      this.error = formatErrorMessage(error);
    } finally {
      this.loading = false;
    }
  }

  private async retryOperation() {
    if (this.config) {
      // If we have config, retry submission
      await this.submitResponse();
    } else {
      // Otherwise, retry fetching survey data
      await this.fetchSurveyData();
    }
  }

  private async submitResponse() {
    if (!this.config || this.submitted || this.rankedItems.length === 0) {
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      const completionTimeSeconds = this.surveyStartTime > 0 ? Math.round((Date.now() - this.surveyStartTime) / 1000) : 0;
      const userAgentInfo = this.getUserAgentInfo();

      const submissionData = {
        surveyPublicKey: this.surveyKey,
        responseData: {
          rankings: this.rankedItems.map(item => ({
            value: item.value,
            title: item.title,
            rank: item.rank,
          })),
        },
        respondentDetails: this.userRespondentDetails,
        userAgent: userAgentInfo,
        completionTime: completionTimeSeconds,
        surveyType: 'sensePriority',
      };

      const response = await fetch(`${RESPONSE_API_ENDPOINT}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      if (!response.ok) {
        // Try to parse error response to get the actual error message
        try {
          const errorData = await response.json();
          throw new Error(errorData.message || `Failed to submit response: ${response.statusText}`);
        } catch (parseError) {
          // If parsing fails, fall back to status text
          throw new Error(`Failed to submit response: ${response.statusText}`);
        }
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Failed to submit response');
      }

      this.submitted = true;
      this.currentStep = SurveyStep.THANK_YOU;
    } catch (error) {
      this.error = formatErrorMessage(error);
    } finally {
      this.loading = false;
    }
  }

  private getUserAgentInfo() {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screenResolution: `${screen.width}x${screen.height}`,
      colorDepth: screen.colorDepth,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timestamp: new Date().toISOString(),
    };
  }

  private nextStep() {
    if (this.currentStep === SurveyStep.PRIORITY_RANKING) {
      if (this.hasRespondentDetailsStep()) {
        this.currentStep = SurveyStep.RESPONDENT_DETAILS;
      } else {
        this.submitResponse();
      }
    } else if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.submitResponse();
    }
  }

  private prevStep() {
    if (this.currentStep === SurveyStep.RESPONDENT_DETAILS) {
      this.currentStep = SurveyStep.PRIORITY_RANKING;
    }
  }

  private hasRespondentDetailsStep(): boolean {
    return this.respondentDetails.length > 0;
  }

  private handleRespondentDetailChange(key: string, value: string) {
    this.userRespondentDetails = {
      ...this.userRespondentDetails,
      [key]: value,
    };
  }

  private isRespondentDetailsValid(): boolean {
    const requiredFields = this.respondentDetails.filter(detail => detail.required);
    return requiredFields.every(field => {
      const value = this.userRespondentDetails[field.value];
      return value && value.trim().length > 0;
    });
  }

  private moveItem(fromIndex: number, toIndex: number) {
    if (fromIndex === toIndex) return;

    const newRankedItems = moveArrayItem(this.rankedItems, fromIndex, toIndex);

    // Update ranks based on new positions
    this.rankedItems = newRankedItems.map((item, index) => ({
      ...item,
      rank: index + 1,
    }));
  }

  private moveItemUp(index: number) {
    if (index > 0) {
      this.moveItem(index, index - 1);
    }
  }

  private moveItemDown(index: number) {
    if (index < this.rankedItems.length - 1) {
      this.moveItem(index, index + 1);
    }
  }

  private handleDragStart(event: DragEvent, index: number) {
    this.draggedIndex = index;
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('text/html', '');
    }
  }

  private handleDragOver(event: DragEvent) {
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
  }

  private handleDrop(event: DragEvent, dropIndex: number) {
    event.preventDefault();
    if (this.draggedIndex !== null && this.draggedIndex !== dropIndex) {
      this.moveItem(this.draggedIndex, dropIndex);
    }
    this.draggedIndex = null;
  }

  private handleDragEnd() {
    this.draggedIndex = null;
  }

  private renderPriorityRankingStep() {
    const isFinalStep = !this.hasRespondentDetailsStep();

    return (
      <div part="step priority-ranking-step">
        <h2 part="heading priority-heading">{this.config?.question || 'Rank these items by priority'}</h2>
        <p part="instructions">Drag and drop items to reorder them, or use the arrow buttons. Rank 1 is highest priority.</p>

        <div part="ranking-container">
          {this.rankedItems.map((item, index) => (
            <div
              part="ranking-item"
              class={{
                dragging: this.draggedIndex === index,
              }}
              draggable="true"
              onDragStart={e => this.handleDragStart(e, index)}
              onDragOver={e => this.handleDragOver(e)}
              onDrop={e => this.handleDrop(e, index)}
              onDragEnd={() => this.handleDragEnd()}
            >
              <div part="rank-number">{item.rank}</div>
              <div part="item-content">
                <div part="item-title">{item.title}</div>
                {item.description && <div part="item-description">{item.description}</div>}
              </div>
              <div part="item-controls">
                <button part="button control-button up-button" onClick={() => this.moveItemUp(index)} disabled={index === 0} title="Move up">
                  ↑
                </button>
                <button part="button control-button down-button" onClick={() => this.moveItemDown(index)} disabled={index === this.rankedItems.length - 1} title="Move down">
                  ↓
                </button>
              </div>
            </div>
          ))}
        </div>

        <div part="button-container">
          <button part="button next-button" onClick={() => this.nextStep()}>
            {isFinalStep ? 'Submit' : 'Next'}
          </button>
        </div>
      </div>
    );
  }

  private renderRespondentDetailsStep() {
    return (
      <div part="step respondent-details-step">
        <h2 part="heading respondent-details-heading">Tell us about yourself</h2>
        <div part="form-container">
          {this.respondentDetails.map(detail => (
            <div part="form-field">
              <label part="form-label">
                {detail.label}
                {detail.required && <span part="required-indicator"> *</span>}
              </label>
              <input
                part="input form-input"
                type="text"
                value={this.userRespondentDetails[detail.value] || ''}
                onInput={e => this.handleRespondentDetailChange(detail.value, (e.target as HTMLInputElement).value)}
                required={detail.required}
              />
            </div>
          ))}
        </div>
        <div part="button-container">
          <button part="button back-button" onClick={() => this.prevStep()}>
            Back
          </button>
          <button part="button submit-button" onClick={() => this.submitResponse()} disabled={!this.isRespondentDetailsValid()}>
            Submit
          </button>
        </div>
      </div>
    );
  }

  private renderThankYouStep() {
    return (
      <div part="step thank-you-step">
        <h2 part="heading thank-you-heading">{this.config?.thankYouMessage || 'Thank you for your response!'}</h2>
        <div part="ranking-summary">
          <h3 part="summary-heading">Your Priority Ranking:</h3>
          <div part="summary-list">
            {this.rankedItems.map(item => (
              <div part="summary-item">
                <span part="summary-rank">#{item.rank}</span>
                <span part="summary-title">{item.title}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  private renderCurrentStep() {
    const stepRenderers = {
      [SurveyStep.PRIORITY_RANKING]: this.renderPriorityRankingStep.bind(this),
      [SurveyStep.RESPONDENT_DETAILS]: this.renderRespondentDetailsStep.bind(this),
      [SurveyStep.THANK_YOU]: this.renderThankYouStep.bind(this),
    };

    const renderer = stepRenderers[this.currentStep] || stepRenderers[SurveyStep.PRIORITY_RANKING];

    return renderer();
  }

  render() {
    if (!isValidKey(this.surveyKey)) {
      return (
        <Host>
          <p part="message error-message">Unable to render survey due to invalid public key</p>
        </Host>
      );
    }

    if (this.loading) {
      return (
        <Host>
          <p part="message loading-message">Loading survey...</p>
        </Host>
      );
    }

    if (this.error) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">{this.error}</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    if (!this.config) {
      return (
        <Host>
          <div part="error-container">
            <p part="message error-message">No survey configuration found</p>
            <button part="button retry-button" onClick={() => this.retryOperation()}>
              Try again
            </button>
          </div>
        </Host>
      );
    }

    return (
      <Host>
        <div part="survey-container">{this.renderCurrentStep()}</div>
      </Host>
    );
  }
}
